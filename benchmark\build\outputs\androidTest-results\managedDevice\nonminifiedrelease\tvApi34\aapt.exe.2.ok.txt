EXECUTING: D:\Android\Sdk\build-tools\35.0.0\aapt.exe dump badging E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\apk\nonMinifiedRelease\benchmark-nonMinifiedRelease.apk
CURRENT_WORKING_DIRECTORY: E:\1-test\tv-samples\JetStreamCompose
START_TIME: 2025-08-27 09:48:14.641
START_TIME-NANOS: 2025-08-27 09:48:14.641179300
ENVIRONMENT:

*****************************************
STDOUT/STDERR BELOW
===================
package: name='com.google.jetstream.benchmark' versionCode='' versionName='' platformBuildVersionName='15' platformBuildVersionCode='35' compileSdkVersion='35' compileSdkVersionCodename='15'
sdkVersion:'28'
targetSdkVersion:'35'
uses-permission: name='android.permission.WRITE_EXTERNAL_STORAGE'
uses-permission: name='android.permission.READ_EXTERNAL_STORAGE'
uses-permission: name='android.permission.QUERY_ALL_PACKAGES'
uses-permission: name='android.permission.INTERNET'
uses-permission: name='android.permission.REORDER_TASKS'
uses-permission: name='com.google.jetstream.benchmark.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION'
application: label='' icon=''
application-debuggable
uses-library:'android.test.runner'
feature-group: label=''
  uses-feature: name='android.hardware.faketouch'
  uses-implied-feature: name='android.hardware.faketouch' reason='default feature for all apps'
other-activities
other-receivers
supports-screens: 'small' 'normal' 'large' 'xlarge'
supports-any-density: 'true'
locales: '--_--' 'af' 'am' 'ar' 'as' 'az' 'be' 'bg' 'bn' 'bs' 'ca' 'cs' 'da' 'de' 'el' 'en-AU' 'en-CA' 'en-GB' 'en-IN' 'en-XC' 'es' 'es-US' 'et' 'eu' 'fa' 'fi' 'fr' 'fr-CA' 'gl' 'gu' 'hi' 'hr' 'hu' 'hy' 'in' 'is' 'it' 'iw' 'ja' 'ka' 'kk' 'km' 'kn' 'ko' 'ky' 'lo' 'lt' 'lv' 'mk' 'ml' 'mn' 'mr' 'ms' 'my' 'nb' 'ne' 'nl' 'or' 'pa' 'pl' 'pt' 'pt-BR' 'pt-PT' 'ro' 'ru' 'si' 'sk' 'sl' 'sq' 'sr' 'sr-Latn' 'sv' 'sw' 'ta' 'te' 'th' 'tl' 'tr' 'uk' 'ur' 'uz' 'vi' 'zh-CN' 'zh-HK' 'zh-TW' 'zu'
densities: '160' '240' '320' '65535'
native-code: 'arm64-v8a' 'armeabi-v7a' 'x86' 'x86_64'
===================
END_TIME: 2025-08-27 09:48:14.672
END_TIME-NANOS: 2025-08-27 09:48:14.672645600
DURATION: 31ms
EXIT CODE: 0
