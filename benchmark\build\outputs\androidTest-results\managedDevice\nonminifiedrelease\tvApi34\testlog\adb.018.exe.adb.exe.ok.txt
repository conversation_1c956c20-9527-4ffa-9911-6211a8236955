EXECUTING: D:\Android\Sdk\platform-tools\adb.exe -H localhost -P 5037 -s emulator-5556 shell getprop
CURRENT_WORKING_DIRECTORY: E:\1-test\tv-samples\JetStreamCompose
START_TIME: 2025-08-27 09:41:54.278
START_TIME-NANOS: 2025-08-27 09:41:54.278955700
ENVIRONMENT:
ANDROID_SDK_HOME=D:\Android\Sdk
ANDROID_ADB=D:\Android\Sdk\platform-tools\adb.exe
*****************************************
STDOUT/STDERR BELOW
===================
[apex.all.ready]: [true]
[bluetooth.device.class_of_device]: [90,2,12]
[bluetooth.profile.a2dp.source.enabled]: [true]
[bluetooth.profile.avrcp.target.enabled]: [true]
[bluetooth.profile.bap.broadcast.assist.enabled]: [false]
[bluetooth.profile.bap.unicast.client.enabled]: [false]
[bluetooth.profile.bas.client.enabled]: [false]
[bluetooth.profile.ccp.server.enabled]: [false]
[bluetooth.profile.csip.set_coordinator.enabled]: [false]
[bluetooth.profile.gatt.enabled]: [true]
[bluetooth.profile.hap.client.enabled]: [false]
[bluetooth.profile.hfp.ag.enabled]: [true]
[bluetooth.profile.hid.device.enabled]: [true]
[bluetooth.profile.hid.host.enabled]: [true]
[bluetooth.profile.map.server.enabled]: [true]
[bluetooth.profile.mcp.server.enabled]: [true]
[bluetooth.profile.opp.enabled]: [true]
[bluetooth.profile.pan.nap.enabled]: [true]
[bluetooth.profile.pan.panu.enabled]: [true]
[bluetooth.profile.pbap.server.enabled]: [true]
[bluetooth.profile.vcp.controller.enabled]: [false]
[bootreceiver.enable]: [1]
[build.version.extensions.ad_services]: [7]
[build.version.extensions.r]: [7]
[build.version.extensions.s]: [7]
[build.version.extensions.t]: [7]
[build.version.extensions.u]: [7]
[cache_key.bluetooth.bluetooth_adapter_get_connection_state]: [-7079799212221630496]
[cache_key.bluetooth.bluetooth_adapter_get_profile_connection_state]: [-7079799212221630495]
[cache_key.bluetooth.bluetooth_adapter_get_state]: [-7079799212221630494]
[cache_key.bluetooth.bluetooth_adapter_is_offloaded_filtering_supported]: [-7079799212221630500]
[cache_key.bluetooth.bluetooth_device_get_bond_state]: [-7079799212221630503]
[cache_key.bluetooth.bluetooth_map_get_connection_state]: [-7079799212221630497]
[cache_key.bluetooth.bluetooth_sap_get_connection_state]: [-7079799212221630501]
[cache_key.display_info]: [307615772144144063]
[cache_key.get_packages_for_uid]: [307615772144144112]
[cache_key.has_system_feature]: [307615772144143284]
[cache_key.is_compat_change_enabled]: [307615772144144102]
[cache_key.is_interactive]: [307615772144143271]
[cache_key.is_power_save_mode]: [307615772144143303]
[cache_key.is_user_unlocked]: [307615772144144080]
[cache_key.location_enabled]: [307615772144144091]
[cache_key.package_info]: [2]
[cache_key.system_server.accounts_data]: [307615772144144085]
[cache_key.system_server.device_policy_manager_caches]: [307615772144144093]
[cache_key.system_server.get_credential_type]: [307615772144143301]
[cache_key.telephony.phone_account_to_subid]: [8908660237470198520]
[cache_key.telephony.subscription_manager_service]: [8908660237470198516]
[dalvik.vm.appimageformat]: [lz4]
[dalvik.vm.dex2oat-Xms]: [64m]
[dalvik.vm.dex2oat-Xmx]: [512m]
[dalvik.vm.dex2oat-max-image-block-size]: [524288]
[dalvik.vm.dex2oat-minidebuginfo]: [true]
[dalvik.vm.dex2oat-resolve-startup-strings]: [true]
[dalvik.vm.dex2oat64.enabled]: [true]
[dalvik.vm.dexopt.secondary]: [true]
[dalvik.vm.dexopt.thermal-cutoff]: [2]
[dalvik.vm.heapgrowthlimit]: [192m]
[dalvik.vm.heapmaxfree]: [8m]
[dalvik.vm.heapminfree]: [512k]
[dalvik.vm.heapsize]: [512m]
[dalvik.vm.heapstartsize]: [8m]
[dalvik.vm.heaptargetutilization]: [0.75]
[dalvik.vm.image-dex2oat-Xms]: [64m]
[dalvik.vm.image-dex2oat-Xmx]: [64m]
[dalvik.vm.isa.x86_64.features]: [default]
[dalvik.vm.isa.x86_64.variant]: [x86_64]
[dalvik.vm.lockprof.threshold]: [500]
[dalvik.vm.madvise.artfile.size]: [**********]
[dalvik.vm.madvise.odexfile.size]: [*********]
[dalvik.vm.madvise.vdexfile.size]: [*********]
[dalvik.vm.minidebuginfo]: [true]
[dalvik.vm.usap_pool_enabled]: [false]
[dalvik.vm.usap_pool_refill_delay_ms]: [3000]
[dalvik.vm.usap_pool_size_max]: [3]
[dalvik.vm.usap_pool_size_min]: [1]
[dalvik.vm.usap_refill_threshold]: [1]
[dalvik.vm.useartservice]: [true]
[dalvik.vm.usejit]: [true]
[debug.atrace.tags.enableflags]: [0]
[debug.force_rtl]: [false]
[debug.hwui.renderer]: [skiagl]
[debug.sf.nobootanimation]: [1]
[debug.sf.vsync_reactor_ignore_present_fences]: [true]
[debug.stagefright.c2inputsurface]: [-1]
[debug.stagefright.ccodec]: [4]
[debug.tracing.battery_stats.brightness]: [2]
[debug.tracing.battery_stats.charging]: [1]
[debug.tracing.battery_stats.phone_signal_strength]: [4]
[debug.tracing.battery_stats.phone_state]: [0]
[debug.tracing.battery_stats.plugged]: [1]
[debug.tracing.battery_stats.running]: [1]
[debug.tracing.battery_stats.screen]: [1]
[debug.tracing.battery_stats.wake_lock]: [1]
[debug.tracing.battery_stats.wifi]: [1]
[debug.tracing.battery_stats.wifi_radio]: [1]
[debug.tracing.battery_stats.wifi_scan]: [1]
[debug.tracing.battery_stats.wifi_signal_strength]: [4]
[debug.tracing.battery_stats.wifi_suppl]: [10]
[debug.tracing.battery_status]: [1]
[debug.tracing.device_state]: [0:DEFAULT]
[debug.tracing.mcc]: [310]
[debug.tracing.mnc]: [260]
[debug.tracing.plug_type]: [1]
[debug.tracing.screen_brightness]: [0.39763778]
[dev.bootcomplete]: [1]
[dev.mnt.blk.metadata]: [vdd1]
[dev.mnt.blk.product]: [vda2]
[dev.mnt.blk.root]: [vda2]
[dev.mnt.blk.system_dlkm]: [vda2]
[dev.mnt.blk.system_ext]: [vda2]
[dev.mnt.blk.vendor]: [vda2]
[dev.mnt.dev.metadata]: [vdd1]
[dev.mnt.dev.product]: [dm-3]
[dev.mnt.dev.root]: [dm-5]
[dev.mnt.dev.system_dlkm]: [dm-1]
[dev.mnt.dev.system_ext]: [dm-2]
[dev.mnt.dev.vendor]: [dm-4]
[dev.mnt.rootdisk.metadata]: [vdd]
[dev.mnt.rootdisk.product]: [vda]
[dev.mnt.rootdisk.root]: [vda]
[dev.mnt.rootdisk.system_dlkm]: [vda]
[dev.mnt.rootdisk.system_ext]: [vda]
[dev.mnt.rootdisk.vendor]: [vda]
[gsm.current.phone-type]: [1]
[gsm.network.type]: [LTE]
[gsm.operator.alpha]: [T-Mobile]
[gsm.operator.iso-country]: [us]
[gsm.operator.isroaming]: [false]
[gsm.operator.numeric]: [310260]
[gsm.sim.operator.alpha]: []
[gsm.sim.operator.iso-country]: []
[gsm.sim.operator.numeric]: []
[gsm.sim.state]: [READY]
[gsm.version.baseband]: [1.0.0.0]
[gsm.version.ril-impl]: [android reference-ril 1.0]
[hwservicemanager.ready]: [true]
[init.svc.adbd]: [running]
[init.svc.android-hardware-media-c2-goldfish-hal-1-0]: [running]
[init.svc.apexd]: [stopped]
[init.svc.apexd-bootstrap]: [stopped]
[init.svc.apexd-snapshotde]: [stopped]
[init.svc.art_boot]: [stopped]
[init.svc.artd]: [stopped]
[init.svc.audioserver]: [running]
[init.svc.bootanim]: [stopped]
[init.svc.boringssl_self_test64]: [stopped]
[init.svc.boringssl_self_test64_vendor]: [stopped]
[init.svc.boringssl_self_test_apex64]: [stopped]
[init.svc.bpfloader]: [stopped]
[init.svc.bt_vhci_forwarder]: [running]
[init.svc.cameraserver]: [running]
[init.svc.console]: [running]
[init.svc.credstore]: [running]
[init.svc.derive_classpath]: [stopped]
[init.svc.derive_sdk]: [stopped]
[init.svc.dhcpclient_wifi]: [running]
[init.svc.dmesgd]: [stopped]
[init.svc.gatekeeperd]: [running]
[init.svc.goldfish-logcat]: [running]
[init.svc.gpu]: [running]
[init.svc.heapprofd]: [stopped]
[init.svc.hidl_memory]: [running]
[init.svc.hwservicemanager]: [running]
[init.svc.idmap2d]: [stopped]
[init.svc.incidentd]: [running]
[init.svc.installd]: [running]
[init.svc.keystore2]: [running]
[init.svc.lmkd]: [running]
[init.svc.logd]: [running]
[init.svc.logd-auditctl]: [stopped]
[init.svc.logd-reinit]: [stopped]
[init.svc.mdnsd]: [running]
[init.svc.media]: [running]
[init.svc.media.swcodec]: [running]
[init.svc.mediadrm]: [running]
[init.svc.mediaextractor]: [running]
[init.svc.mediametrics]: [running]
[init.svc.netd]: [running]
[init.svc.neuralnetworks_hal_service_aidl_sample_all]: [running]
[init.svc.neuralnetworks_hal_service_aidl_sample_limited]: [running]
[init.svc.neuralnetworks_hal_service_shim_sample]: [running]
[init.svc.odsign]: [stopped]
[init.svc.prng_seeder]: [running]
[init.svc.qemu-adb-keys]: [stopped]
[init.svc.qemu-adb-setup]: [stopped]
[init.svc.qemu-device-state]: [stopped]
[init.svc.qemu-props]: [running]
[init.svc.qemu-props-bootcomplete]: [stopped]
[init.svc.ranchu-net]: [stopped]
[init.svc.ranchu-setup]: [stopped]
[init.svc.servicemanager]: [running]
[init.svc.statsd]: [running]
[init.svc.storaged]: [running]
[init.svc.surfaceflinger]: [running]
[init.svc.system_suspend]: [running]
[init.svc.tombstoned]: [running]
[init.svc.traced]: [running]
[init.svc.traced_perf]: [stopped]
[init.svc.traced_probes]: [running]
[init.svc.ueventd]: [running]
[init.svc.update_verifier_nonencrypted]: [stopped]
[init.svc.usbd]: [stopped]
[init.svc.vold]: [running]
[init.svc.wificond]: [running]
[init.svc.wpa_supplicant]: [running]
[init.svc.zygote]: [running]
[log.tag.APM_AudioPolicyManager]: [D]
[log.tag.stats_log]: [I]
[logd.logpersistd.enable]: [true]
[logd.ready]: [true]
[net.bt.name]: [Android]
[odsign.key.done]: [1]
[odsign.verification.done]: [1]
[odsign.verification.success]: [1]
[partition.system.verified]: [2]
[partition.system.verified.check_at_most_once]: [0]
[partition.system.verified.hash_alg]: [sha256]
[partition.system.verified.root_digest]: [6f7c5132f38a67a471ea7714a7d6d9c21729041afcf6c6080c2937036bb8d05f]
[persist.debug.dalvik.vm.core_platform_api_policy]: [just-warn]
[persist.sys.boot.reason]: []
[persist.sys.boot.reason.history]: [reboot,factory_reset,1756202905
reboot,1756202889]
[persist.sys.dalvik.vm.lib.2]: [libart.so]
[persist.sys.disable_rescue]: [true]
[persist.sys.displayinset.top]: [0]
[persist.sys.fuse]: [true]
[persist.sys.lmk.reportkills]: [true]
[persist.sys.timezone]: [GMT]
[persist.sys.usb.config]: [adb]
[persist.traced.enable]: [1]
[persist.wm.extensions.enabled]: [true]
[pm.dexopt.ab-ota]: [speed-profile]
[pm.dexopt.bg-dexopt]: [speed-profile]
[pm.dexopt.boot-after-mainline-update]: [verify]
[pm.dexopt.boot-after-ota]: [verify]
[pm.dexopt.cmdline]: [verify]
[pm.dexopt.first-boot]: [verify]
[pm.dexopt.inactive]: [verify]
[pm.dexopt.install]: [speed-profile]
[pm.dexopt.install-bulk]: [speed-profile]
[pm.dexopt.install-bulk-downgraded]: [verify]
[pm.dexopt.install-bulk-secondary]: [verify]
[pm.dexopt.install-bulk-secondary-downgraded]: [extract]
[pm.dexopt.install-fast]: [skip]
[pm.dexopt.post-boot]: [extract]
[pm.dexopt.shared]: [speed]
[qemu.sf.lcd_density]: [320]
[remote_provisioning.enable_rkpd]: [true]
[remote_provisioning.hostname]: [remoteprovisioning.googleapis.com]
[ro.actionable_compatible_property.enabled]: [true]
[ro.adb.secure]: [0]
[ro.allow.mock.location]: [0]
[ro.apex.updatable]: [true]
[ro.baseband]: [unknown]
[ro.board.platform]: []
[ro.boot.avb_version]: [1.2]
[ro.boot.boot_devices]: [pci0000:00/0000:00:03.0 pci0000:00/0000:00:06.0]
[ro.boot.bootreason]: [reboot,factory_reset]
[ro.boot.dalvik.vm.heapsize]: [512m]
[ro.boot.debug.hwui.renderer]: [skiagl]
[ro.boot.debug.sf.nobootanimation]: [1]
[ro.boot.dynamic_partitions]: [true]
[ro.boot.hardware]: [ranchu]
[ro.boot.hardware.gltransport]: [pipe]
[ro.boot.hardware.vulkan]: [ranchu]
[ro.boot.logcat]: [*:V]
[ro.boot.opengles.version]: [196609]
[ro.boot.qemu]: [1]
[ro.boot.qemu.avd_name]: [dev34_default_x86_64_Television__1080p_]
[ro.boot.qemu.camera_hq_edge_processing]: [0]
[ro.boot.qemu.camera_protocol_ver]: [1]
[ro.boot.qemu.cpuvulkan.version]: [4202496]
[ro.boot.qemu.gltransport.drawFlushInterval]: [800]
[ro.boot.qemu.gltransport.name]: [pipe]
[ro.boot.qemu.hwcodec.avcdec]: [2]
[ro.boot.qemu.hwcodec.hevcdec]: [2]
[ro.boot.qemu.hwcodec.vpxdec]: [2]
[ro.boot.qemu.settings.system.screen_off_timeout]: [2147483647]
[ro.boot.qemu.virtiowifi]: [1]
[ro.boot.qemu.vsync]: [60]
[ro.boot.serialno]: [EMULATOR36X1X9X0]
[ro.boot.vbmeta.digest]: [8bee4f6105b6acd7de887b1ee6629ab07066df02ee9bbca07663adffdad97fc1]
[ro.boot.vbmeta.hash_alg]: [sha256]
[ro.boot.vbmeta.size]: [6656]
[ro.boot.veritymode]: [enforcing]
[ro.bootimage.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.bootimage.build.date.utc]: [1702767290]
[ro.bootimage.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.bootimage.build.id]: [UE1A.230829.036.A1]
[ro.bootimage.build.tags]: [test-keys]
[ro.bootimage.build.type]: [userdebug]
[ro.bootimage.build.version.incremental]: [11228894]
[ro.bootimage.build.version.release]: [14]
[ro.bootimage.build.version.release_or_codename]: [14]
[ro.bootimage.build.version.sdk]: [34]
[ro.bootloader]: [unknown]
[ro.bootmode]: [unknown]
[ro.build.ab_update]: [false]
[ro.build.characteristics]: [emulator]
[ro.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.build.date.utc]: [1702767290]
[ro.build.description]: [sdk_phone64_x86_64-userdebug 14 UE1A.230829.036.A1 11228894 test-keys]
[ro.build.display.id]: [sdk_phone64_x86_64-userdebug 14 UE1A.230829.036.A1 11228894 test-keys]
[ro.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.build.flavor]: [sdk_phone64_x86_64-userdebug]
[ro.build.host]: [abfarm-release-2004-0149]
[ro.build.id]: [UE1A.230829.036.A1]
[ro.build.product]: [emu64x]
[ro.build.tags]: [test-keys]
[ro.build.type]: [userdebug]
[ro.build.user]: [android-build]
[ro.build.version.all_codenames]: [REL]
[ro.build.version.base_os]: []
[ro.build.version.codename]: [REL]
[ro.build.version.incremental]: [11228894]
[ro.build.version.known_codenames]: [Base,Base11,Cupcake,Donut,Eclair,Eclair01,EclairMr1,Froyo,Gingerbread,GingerbreadMr1,Honeycomb,HoneycombMr1,HoneycombMr2,IceCreamSandwich,IceCreamSandwichMr1,JellyBean,JellyBeanMr1,JellyBeanMr2,Kitkat,KitkatWatch,Lollipop,LollipopMr1,M,N,NMr1,O,OMr1,P,Q,R,S,Sv2,Tiramisu,UpsideDownCake]
[ro.build.version.min_supported_target_sdk]: [28]
[ro.build.version.preview_sdk]: [0]
[ro.build.version.preview_sdk_fingerprint]: [REL]
[ro.build.version.release]: [14]
[ro.build.version.release_or_codename]: [14]
[ro.build.version.release_or_preview_display]: [14]
[ro.build.version.sdk]: [34]
[ro.build.version.security_patch]: [2023-09-05]
[ro.carrier]: [unknown]
[ro.com.android.dataroaming]: [true]
[ro.com.google.locationfeatures]: [1]
[ro.config.alarm_alert]: [Alarm_Classic.ogg]
[ro.config.notification_sound]: [pixiedust.ogg]
[ro.config.ringtone]: [Ring_Synth_04.ogg]
[ro.control_privapp_permissions]: [enforce]
[ro.cp_system_other_odex]: [0]
[ro.cpuvulkan.version]: [4202496]
[ro.crypto.dm_default_key.options_format.version]: [2]
[ro.crypto.metadata.enabled]: [true]
[ro.crypto.state]: [encrypted]
[ro.crypto.type]: [file]
[ro.crypto.uses_fs_ioc_add_encryption_key]: [true]
[ro.crypto.volume.filenames_mode]: [aes-256-cts]
[ro.dalvik.vm.enable_uffd_gc]: [false]
[ro.dalvik.vm.native.bridge]: [0]
[ro.debuggable]: [1]
[ro.force.debuggable]: [0]
[ro.fuse.bpf.is_running]: [true]
[ro.hardware]: [ranchu]
[ro.hardware.egl]: [emulation]
[ro.hardware.gralloc]: [ranchu]
[ro.hardware.power]: [ranchu]
[ro.hardware.vulkan]: [ranchu]
[ro.hwui.use_vulkan]: []
[ro.kernel.qemu]: [1]
[ro.kernel.version]: [6.1]
[ro.logd.kernel]: [true]
[ro.logd.size]: [1M]
[ro.logd.size.stats]: [64K]
[ro.monkey]: [1]
[ro.nnapi.extensions.deny_on_product]: [true]
[ro.odm.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.odm.build.date.utc]: [1702767290]
[ro.odm.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.odm.build.version.incremental]: [11228894]
[ro.opengles.version]: [196609]
[ro.organization_owned]: [false]
[ro.postinstall.fstab.prefix]: [/system]
[ro.product.board]: [goldfish_x86_64]
[ro.product.bootimage.brand]: [Android]
[ro.product.bootimage.device]: [emu64x]
[ro.product.bootimage.manufacturer]: [unknown]
[ro.product.bootimage.model]: [Android SDK built for x86_64]
[ro.product.bootimage.name]: [sdk_phone64_x86_64]
[ro.product.brand]: [Android]
[ro.product.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.product.build.date.utc]: [1702767290]
[ro.product.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.product.build.id]: [UE1A.230829.036.A1]
[ro.product.build.tags]: [test-keys]
[ro.product.build.type]: [userdebug]
[ro.product.build.version.incremental]: [11228894]
[ro.product.build.version.release]: [14]
[ro.product.build.version.release_or_codename]: [14]
[ro.product.build.version.sdk]: [34]
[ro.product.cpu.abi]: [x86_64]
[ro.product.cpu.abilist]: [x86_64]
[ro.product.cpu.abilist32]: []
[ro.product.cpu.abilist64]: [x86_64]
[ro.product.cpu.pagesize.max]: [65536]
[ro.product.device]: [emu64x]
[ro.product.first_api_level]: [34]
[ro.product.locale]: [en-US]
[ro.product.manufacturer]: [unknown]
[ro.product.model]: [Android SDK built for x86_64]
[ro.product.name]: [sdk_phone64_x86_64]
[ro.product.odm.brand]: [Android]
[ro.product.odm.device]: [emu64x]
[ro.product.odm.manufacturer]: [unknown]
[ro.product.odm.model]: [Android SDK built for x86_64]
[ro.product.odm.name]: [sdk_phone64_x86_64]
[ro.product.product.brand]: [Android]
[ro.product.product.device]: [emu64x]
[ro.product.product.manufacturer]: [unknown]
[ro.product.product.model]: [Android SDK built for x86_64]
[ro.product.product.name]: [sdk_phone64_x86_64]
[ro.product.system.brand]: [Android]
[ro.product.system.device]: [generic]
[ro.product.system.manufacturer]: [Android]
[ro.product.system.model]: [mainline]
[ro.product.system.name]: [mainline]
[ro.product.system_dlkm.brand]: [Android]
[ro.product.system_dlkm.device]: [emu64x]
[ro.product.system_dlkm.manufacturer]: [unknown]
[ro.product.system_dlkm.model]: [Android SDK built for x86_64]
[ro.product.system_dlkm.name]: [sdk_phone64_x86_64]
[ro.product.system_ext.brand]: [Android]
[ro.product.system_ext.device]: [emu64x]
[ro.product.system_ext.manufacturer]: [unknown]
[ro.product.system_ext.model]: [Android SDK built for x86_64]
[ro.product.system_ext.name]: [sdk_phone64_x86_64]
[ro.product.vendor.brand]: [Android]
[ro.product.vendor.device]: [emu64x]
[ro.product.vendor.manufacturer]: [unknown]
[ro.product.vendor.model]: [Android SDK built for x86_64]
[ro.product.vendor.name]: [sdk_phone64_x86_64]
[ro.product.vendor_dlkm.brand]: [Android]
[ro.product.vendor_dlkm.device]: [emu64x]
[ro.product.vendor_dlkm.manufacturer]: [unknown]
[ro.product.vendor_dlkm.model]: [Android SDK built for x86_64]
[ro.product.vendor_dlkm.name]: [sdk_phone64_x86_64]
[ro.product.vndk.version]: [34]
[ro.property_service.version]: [2]
[ro.revision]: [0]
[ro.secure]: [1]
[ro.serialno]: [EMULATOR36X1X9X0]
[ro.setupwizard.mode]: [DISABLED]
[ro.soc.manufacturer]: [AOSP]
[ro.soc.model]: [ranchu]
[ro.surface_flinger.has_HDR_display]: [false]
[ro.surface_flinger.has_wide_color_display]: [false]
[ro.surface_flinger.protected_contents]: [false]
[ro.surface_flinger.supports_background_blur]: [1]
[ro.surface_flinger.use_color_management]: [false]
[ro.system.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.system.build.date.utc]: [1702767290]
[ro.system.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.system.build.id]: [UE1A.230829.036.A1]
[ro.system.build.tags]: [test-keys]
[ro.system.build.type]: [userdebug]
[ro.system.build.version.incremental]: [11228894]
[ro.system.build.version.release]: [14]
[ro.system.build.version.release_or_codename]: [14]
[ro.system.build.version.sdk]: [34]
[ro.system.product.cpu.abilist]: [x86_64]
[ro.system.product.cpu.abilist32]: []
[ro.system.product.cpu.abilist64]: [x86_64]
[ro.system_dlkm.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.system_dlkm.build.date.utc]: [1702767290]
[ro.system_dlkm.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.system_dlkm.build.id]: [UE1A.230829.036.A1]
[ro.system_dlkm.build.tags]: [test-keys]
[ro.system_dlkm.build.type]: [userdebug]
[ro.system_dlkm.build.version.incremental]: [11228894]
[ro.system_dlkm.build.version.release]: [14]
[ro.system_dlkm.build.version.release_or_codename]: [14]
[ro.system_dlkm.build.version.sdk]: [34]
[ro.system_ext.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.system_ext.build.date.utc]: [1702767290]
[ro.system_ext.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.system_ext.build.id]: [UE1A.230829.036.A1]
[ro.system_ext.build.tags]: [test-keys]
[ro.system_ext.build.type]: [userdebug]
[ro.system_ext.build.version.incremental]: [11228894]
[ro.system_ext.build.version.release]: [14]
[ro.system_ext.build.version.release_or_codename]: [14]
[ro.system_ext.build.version.sdk]: [34]
[ro.test_harness]: [1]
[ro.treble.enabled]: [true]
[ro.vendor.api_level]: [34]
[ro.vendor.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.vendor.build.date.utc]: [1702767290]
[ro.vendor.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.vendor.build.id]: [UE1A.230829.036.A1]
[ro.vendor.build.security_patch]: []
[ro.vendor.build.tags]: [test-keys]
[ro.vendor.build.type]: [userdebug]
[ro.vendor.build.version.incremental]: [11228894]
[ro.vendor.build.version.release]: [14]
[ro.vendor.build.version.release_or_codename]: [14]
[ro.vendor.build.version.sdk]: [34]
[ro.vendor.product.cpu.abilist]: [x86_64]
[ro.vendor.product.cpu.abilist32]: []
[ro.vendor.product.cpu.abilist64]: [x86_64]
[ro.vendor_dlkm.build.date]: [Sat Dec 16 22:54:50 UTC 2023]
[ro.vendor_dlkm.build.date.utc]: [1702767290]
[ro.vendor_dlkm.build.fingerprint]: [Android/sdk_phone64_x86_64/emu64x:14/UE1A.230829.036.A1/11228894:userdebug/test-keys]
[ro.vendor_dlkm.build.id]: [UE1A.230829.036.A1]
[ro.vendor_dlkm.build.tags]: [test-keys]
[ro.vendor_dlkm.build.type]: [userdebug]
[ro.vendor_dlkm.build.version.incremental]: [11228894]
[ro.vendor_dlkm.build.version.release]: [14]
[ro.vendor_dlkm.build.version.release_or_codename]: [14]
[ro.vendor_dlkm.build.version.sdk]: [34]
[ro.vndk.version]: [34]
[ro.wifi.channels]: []
[ro.zygote]: [zygote64]
[ro.zygote.disable_gl_preload]: [1]
[security.perf_harden]: [1]
[selinux.restorecon_recursive]: [/data/misc_ce/0]
[service.sf.present_timestamp]: [0]
[servicemanager.ready]: [true]
[sys.boot.reason]: [reboot,factory_reset]
[sys.boot.reason.last]: [reboot]
[sys.boot_completed]: [1]
[sys.bootstat.first_boot_completed]: [1]
[sys.fuse.transcode_enabled]: [true]
[sys.init.perf_lsm_hooks]: [1]
[sys.rescue_boot_count]: [1]
[sys.sysctl.extra_free_kbytes]: [24300]
[sys.system_server.start_count]: [1]
[sys.system_server.start_elapsed]: [25422]
[sys.system_server.start_uptime]: [25422]
[sys.usb.config]: [adb]
[sys.usb.configfs]: [0]
[sys.usb.controller]: [dummy_udc.0]
[sys.usb.state]: [adb]
[sys.use_memfd]: [false]
[sys.user.0.ce_available]: [true]
[sys.wifitracing.started]: [1]
[vendor.qemu.sf.fake_camera]: [back]
[vendor.qemu.timezone]: [Unknown/Unknown]
[vendor.qemu.vport.bluetooth]: [/dev/vport7p2]
[vendor.qemu.vport.modem]: [/dev/vport8p1]
[vold.has_adoptable]: [1]
[vold.has_compress]: [0]
[vold.has_quota]: [1]
[vold.has_reserved]: [1]

===================
END_TIME: 2025-08-27 09:41:54.528
END_TIME-NANOS: 2025-08-27 09:41:54.528281500
DURATION: 249ms
EXIT CODE: 0
