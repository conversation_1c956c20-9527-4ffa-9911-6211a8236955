package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.google.jetstream.JetStreamApplication",
    rootPackage = "com.google.jetstream",
    originatingRoot = "com.google.jetstream.JetStreamApplication",
    originatingRootPackage = "com.google.jetstream",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "JetStreamApplication",
    originatingRootSimpleNames = "JetStreamApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_google_jetstream_JetStreamApplication {
}
