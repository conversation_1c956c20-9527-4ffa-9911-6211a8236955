1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.google.jetstream.benchmark" >
4
5    <uses-sdk
5-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:5:5-74
6        android:minSdkVersion="28"
6-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:5:15-41
7        android:targetSdkVersion="35" />
7-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:5:42-71
8
9    <instrumentation
9-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:11:5-15:80
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:11:22-76
11        android:functionalTest="false"
11-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:14:22-52
12        android:handleProfiling="false"
12-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:13:22-53
13        android:label="Tests for com.google.jetstream.benchmark"
13-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:15:22-78
14        android:targetPackage="com.google.jetstream.benchmark" />
14-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:12:22-76
15    <!--
16    This is needed to write benchmark report data to an external directory during instrumented
17    tests to allow the accompanying androidx.benchmark gradle plugin to pull the reports onto host
18     machine after the tests are done running.
19    -->
20    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
20-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:27:5-81
20-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:27:22-78
21    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
22    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
22-->[androidx.benchmark:benchmark-macro:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\AndroidManifest.xml:23:5-25:53
22-->[androidx.benchmark:benchmark-macro:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\AndroidManifest.xml:24:9-61
23    <!--
24         Internet permission is required for perfetto trace shell processor http server but
25        it's used to reach localhost only
26    -->
27    <uses-permission android:name="android.permission.INTERNET" />
27-->[androidx.benchmark:benchmark-macro:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\AndroidManifest.xml:31:5-67
27-->[androidx.benchmark:benchmark-macro:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\AndroidManifest.xml:31:22-64
28    <uses-permission android:name="android.permission.REORDER_TASKS" />
28-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
28-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
29
30    <queries>
30-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:24:5-28:15
31        <package android:name="androidx.test.orchestrator" />
31-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:25:9-62
31-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:25:18-59
32        <package android:name="androidx.test.services" />
32-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:26:9-58
32-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:26:18-55
33        <package android:name="com.google.android.apps.common.testing.services" />
33-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:27:9-83
33-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bbb7ff9f3a0b98125194904d3ca179b0\transformed\runner-1.6.1\AndroidManifest.xml:27:18-80
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.google.jetstream.benchmark.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.google.jetstream.benchmark.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:7:5-9:19
43        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
43-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
44        android:debuggable="true"
45        android:extractNativeLibs="false"
46        android:networkSecurityConfig="@xml/network_security_config" >
46-->[androidx.benchmark:benchmark-macro:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\AndroidManifest.xml:42:18-78
47        <uses-library android:name="android.test.runner" />
47-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:8:9-60
47-->E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\tmp\manifest\benchmarkRelease\tempFile1ProcessTestManifest176719712077545473.xml:8:23-57
48        <!-- Activity used to block background content while benchmarks are running -->
49        <activity
49-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:32:9-36:20
50            android:name="androidx.benchmark.IsolationActivity"
50-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:33:13-64
51            android:exported="true"
51-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:34:13-36
52            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
52-->[androidx.benchmark:benchmark-common:1.3.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\AndroidManifest.xml:35:13-77
53        </activity>
54        <activity
54-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
55            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
55-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
56            android:exported="true"
56-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
57            android:theme="@style/WhiteBackgroundTheme" >
57-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
58            <intent-filter android:priority="-100" >
58-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
58-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
59                <category android:name="android.intent.category.LAUNCHER" />
59-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
59-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
60            </intent-filter>
61        </activity>
62        <activity
62-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
63            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
63-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
64            android:exported="true"
64-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
65            android:theme="@style/WhiteBackgroundTheme" >
65-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
66            <intent-filter android:priority="-100" >
66-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
66-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
67                <category android:name="android.intent.category.LAUNCHER" />
67-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
67-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
68            </intent-filter>
69        </activity>
70        <activity
70-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
71            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
71-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
72            android:exported="true"
72-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
73            android:theme="@style/WhiteBackgroundDialogTheme" >
73-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
74            <intent-filter android:priority="-100" >
74-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
74-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
75                <category android:name="android.intent.category.LAUNCHER" />
75-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
75-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
76            </intent-filter>
77        </activity>
78
79        <receiver
79-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:24:9-37:20
80            android:name="androidx.tracing.perfetto.TracingReceiver"
80-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:25:13-69
81            android:directBootAware="false"
81-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:26:13-44
82            android:enabled="true"
82-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:27:13-35
83            android:exported="true"
83-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:28:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:29:13-57
85
86            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
87            <intent-filter>
87-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:32:13-36:29
88                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
88-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:17-90
88-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:33:25-87
89                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
89-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:17-101
89-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:34:25-98
90                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
90-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:17-102
90-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:35:25-99
91            </intent-filter>
92        </receiver>
93
94        <provider
94-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:39:9-47:20
95            android:name="androidx.startup.InitializationProvider"
95-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:40:13-67
96            android:authorities="com.google.jetstream.benchmark.androidx-startup"
96-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:41:13-68
97            android:exported="false" >
97-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:42:13-37
98            <meta-data
98-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:44:13-46:52
99                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
99-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:45:17-83
100                android:value="androidx.startup" />
100-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:46:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <receiver
106-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:49:9-55:20
107            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
107-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:50:13-92
108            android:directBootAware="false"
108-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:51:13-44
109            android:enabled="false"
109-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:52:13-36
110            android:exported="false" >
110-->[androidx.tracing:tracing-perfetto:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\AndroidManifest.xml:53:13-37
111        </receiver>
112        <receiver
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
113            android:name="androidx.profileinstaller.ProfileInstallReceiver"
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
114            android:directBootAware="false"
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
115            android:enabled="true"
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
116            android:exported="true"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
117            android:permission="android.permission.DUMP" >
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
119                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
122                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
125                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
128                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
129            </intent-filter>
130        </receiver>
131    </application>
132
133</manifest>
