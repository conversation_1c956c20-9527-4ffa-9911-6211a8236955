1<?xml version="1.0" encoding="utf-8"?>
2<!--
3 Copyright 2023 Google LLC
4
5 Licensed under the Apache License, Version 2.0 (the "License");
6 you may not use this file except in compliance with the License.
7 You may obtain a copy of the License at
8
9https://www.apache.org/licenses/LICENSE-2.0
10
11 Unless required by applicable law or agreed to in writing, software
12 distributed under the License is distributed on an "AS IS" BASIS,
13 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
14 See the License for the specific language governing permissions and
15 limitations under the License.
16-->
17<manifest xmlns:android="http://schemas.android.com/apk/res/android"
18    package="com.google.jetstream"
19    android:versionCode="1"
20    android:versionName="1.0" >
21
22    <uses-sdk
23        android:minSdkVersion="28"
24        android:targetSdkVersion="35" />
25
26    <uses-feature
26-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:21:5-23:36
27        android:name="android.software.leanback"
27-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:22:9-49
28        android:required="false" />
28-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:23:9-33
29    <uses-feature
29-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:25:5-27:36
30        android:name="android.hardware.touchscreen"
30-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:26:9-52
31        android:required="false" />
31-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:27:9-33
32
33    <uses-permission android:name="android.permission.INTERNET" />
33-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:29:5-67
33-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:29:22-64
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[androidx.media3:media3-exoplayer:1.6.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75717f9e2674b828a5844a6ecf4739d8\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:22:5-79
34-->[androidx.media3:media3-exoplayer:1.6.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\75717f9e2674b828a5844a6ecf4739d8\transformed\media3-exoplayer-1.6.0-beta01\AndroidManifest.xml:22:22-76
35
36    <permission
36-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.google.jetstream.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.google.jetstream.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:31:5-59:19
43        android:name="com.google.jetstream.JetStreamApplication"
43-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:32:9-45
44        android:allowBackup="true"
44-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:33:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7c47bd247230d16e6e10bfe483eda844\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
46        android:banner="@mipmap/app_banner"
46-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:34:9-44
47        android:dataExtractionRules="@xml/data_extraction_rules"
47-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:35:9-65
48        android:debuggable="true"
49        android:extractNativeLibs="false"
50        android:fullBackupContent="@xml/backup_rules"
50-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:36:9-54
51        android:icon="@mipmap/ic_launcher"
51-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:37:9-43
52        android:label="@string/app_name"
52-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:38:9-41
53        android:supportsRtl="true"
53-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:39:9-35
54        android:testOnly="true"
55        android:theme="@style/Theme.App.Starting"
55-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:40:9-50
56        android:usesCleartextTraffic="true" >
56-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:41:9-44
57
58        <!-- Enable profiling by macrobenchmark -->
59        <profileable android:shell="true" />
59-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:45:9-47:36
59-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:46:13-33
60
61        <activity
61-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:49:9-58:20
62            android:name="com.google.jetstream.MainActivity"
62-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:50:13-41
63            android:exported="true"
63-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:51:13-36
64            android:theme="@style/Theme.App.Starting" >
64-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:52:13-54
65            <intent-filter>
65-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:53:13-57:29
66                <action android:name="android.intent.action.MAIN" />
66-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:54:17-69
66-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:54:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:55:17-77
68-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:55:27-74
69                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
69-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:56:17-86
69-->E:\1-test\tv-samples\JetStreamCompose\jetstream\src\main\AndroidManifest.xml:56:27-83
70            </intent-filter>
71        </activity>
72        <activity
72-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3898bb02d3f69665445328f33fe88507\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
73            android:name="androidx.compose.ui.tooling.PreviewActivity"
73-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3898bb02d3f69665445328f33fe88507\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
74            android:exported="true" />
74-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3898bb02d3f69665445328f33fe88507\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
75
76        <provider
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
77            android:name="androidx.startup.InitializationProvider"
77-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
78            android:authorities="com.google.jetstream.androidx-startup"
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
79            android:exported="false" >
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
80            <meta-data
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.emoji2.text.EmojiCompatInitializer"
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
82                android:value="androidx.startup" />
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\740fe478d8d6de63fb1c0dd044111fdf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
83            <meta-data
83-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd6a15e7999d7b2fc84ea12c473a106\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
84                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
84-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd6a15e7999d7b2fc84ea12c473a106\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
85                android:value="androidx.startup" />
85-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bd6a15e7999d7b2fc84ea12c473a106\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
86            <meta-data
86-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:29:13-31:52
87                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
87-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:30:17-85
88                android:value="androidx.startup" />
88-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:31:17-49
89        </provider>
90
91        <receiver
91-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:34:9-52:20
92            android:name="androidx.profileinstaller.ProfileInstallReceiver"
92-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:35:13-76
93            android:directBootAware="false"
93-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:36:13-44
94            android:enabled="true"
94-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:37:13-35
95            android:exported="true"
95-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:38:13-36
96            android:permission="android.permission.DUMP" >
96-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:39:13-57
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:40:13-42:29
98                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:17-91
98-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:41:25-88
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:43:13-45:29
101                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
101-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:17-85
101-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:44:25-82
102            </intent-filter>
103            <intent-filter>
103-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:46:13-48:29
104                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
104-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:17-88
104-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:47:25-85
105            </intent-filter>
106            <intent-filter>
106-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:49:13-51:29
107                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
107-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:17-95
107-->[androidx.profileinstaller:profileinstaller:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\163cf4d676acbc6c3d35bccec5aef320\transformed\profileinstaller-1.4.1\AndroidManifest.xml:50:25-92
108            </intent-filter>
109        </receiver>
110    </application>
111
112</manifest>
