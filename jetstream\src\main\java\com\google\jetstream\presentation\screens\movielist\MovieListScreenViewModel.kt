/*
 * Copyright 2023 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.jetstream.presentation.screens.movielist

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.jetstream.data.entities.MovieList
import com.google.jetstream.data.repositories.MovieRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class)
@HiltViewModel
class MovieListScreenViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val movieRepository: MovieRepository
) : ViewModel() {

    private val typeFlow = savedStateHandle.getStateFlow<String?>(
        MovieListScreen.TypeBundleKey,
        null
    )

    val uiState = typeFlow.flatMapLatest { type ->
        if (type == null) {
            flowOf(MovieListScreenUiState.Error)
        } else {
            when (type) {
                "movies" -> {
                    movieRepository.getPopularFilmsThisWeek().map { movieList ->
                        MovieListScreenUiState.Done("电影", movieList)
                    }
                }
                "tvshows" -> {
                    movieRepository.getTVShows().map { movieList ->
                        MovieListScreenUiState.Done("电视剧", movieList)
                    }
                }
                "favourites" -> {
                    movieRepository.getFavouriteMovies().map { movieList ->
                        MovieListScreenUiState.Done("收藏", movieList)
                    }
                }
                else -> {
                    flowOf(MovieListScreenUiState.Error)
                }
            }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5_000),
        initialValue = MovieListScreenUiState.Loading
    )
}

sealed interface MovieListScreenUiState {
    data object Loading : MovieListScreenUiState
    data object Error : MovieListScreenUiState
    data class Done(
        val title: String,
        val movieList: MovieList
    ) : MovieListScreenUiState
}
