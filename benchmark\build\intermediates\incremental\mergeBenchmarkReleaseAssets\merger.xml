<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="androidx.benchmark:benchmark-macro:1.3.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\assets"><file name="trace_processor_shell_aarch64" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\assets\trace_processor_shell_aarch64"/><file name="trace_processor_shell_arm" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\assets\trace_processor_shell_arm"/><file name="trace_processor_shell_x86" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\assets\trace_processor_shell_x86"/><file name="trace_processor_shell_x86_64" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\assets\trace_processor_shell_x86_64"/></source></dataSet><dataSet config="androidx.benchmark:benchmark-common:1.3.3" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\assets"><file name="tracebox_aarch64" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\assets\tracebox_aarch64"/><file name="tracebox_arm" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\assets\tracebox_arm"/><file name="tracebox_x86" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\assets\tracebox_x86"/><file name="tracebox_x86_64" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\assets\tracebox_x86_64"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\tv-samples\JetStreamCompose\benchmark\src\main\assets"/></dataSet><dataSet config="benchmarkRelease" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\tv-samples\JetStreamCompose\benchmark\src\benchmarkRelease\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\shader_assets\benchmarkRelease\compileBenchmarkReleaseShaders\out"/></dataSet></merger>