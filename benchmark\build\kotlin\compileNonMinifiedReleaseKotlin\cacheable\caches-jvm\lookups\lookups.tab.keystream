  BaselineProfileMode androidx.benchmark.macro  CompilationMode androidx.benchmark.macro  MacrobenchmarkScope androidx.benchmark.macro  StartupMode androidx.benchmark.macro  StartupTimingMetric androidx.benchmark.macro  Require ,androidx.benchmark.macro.BaselineProfileMode  	Companion (androidx.benchmark.macro.CompilationMode  None (androidx.benchmark.macro.CompilationMode  Partial (androidx.benchmark.macro.CompilationMode  By ,androidx.benchmark.macro.MacrobenchmarkScope  CAROUSEL_DESCRIPTION ,androidx.benchmark.macro.MacrobenchmarkScope  CATEGORIES_TAB_LABEL ,androidx.benchmark.macro.MacrobenchmarkScope  FAVOURITES_TAB_LABEL ,androidx.benchmark.macro.MacrobenchmarkScope  INITIAL_WAIT_TIMEOUT ,androidx.benchmark.macro.MacrobenchmarkScope  MOVIES_TAB_LABEL ,androidx.benchmark.macro.MacrobenchmarkScope  PROFILE_BUTTON_DESCRIPTION ,androidx.benchmark.macro.MacrobenchmarkScope  SEARCH_TAB_DESCRIPTION ,androidx.benchmark.macro.MacrobenchmarkScope  SHOWS_TAB_LABEL ,androidx.benchmark.macro.MacrobenchmarkScope  TV_SHOWS_CHIP_LABEL ,androidx.benchmark.macro.MacrobenchmarkScope  Until ,androidx.benchmark.macro.MacrobenchmarkScope  WAIT_TIMEOUT ,androidx.benchmark.macro.MacrobenchmarkScope  device ,androidx.benchmark.macro.MacrobenchmarkScope  	pressHome ,androidx.benchmark.macro.MacrobenchmarkScope  repeat ,androidx.benchmark.macro.MacrobenchmarkScope  run ,androidx.benchmark.macro.MacrobenchmarkScope  startActivityAndWait ,androidx.benchmark.macro.MacrobenchmarkScope  COLD $androidx.benchmark.macro.StartupMode  BaselineProfileRule androidx.benchmark.macro.junit4  MacrobenchmarkRule androidx.benchmark.macro.junit4  collect 3androidx.benchmark.macro.junit4.BaselineProfileRule  measureRepeated 2androidx.benchmark.macro.junit4.MacrobenchmarkRule  
AndroidJUnit4 androidx.test.ext.junit.runners  By androidx.test.uiautomator  
BySelector androidx.test.uiautomator  SearchCondition androidx.test.uiautomator  UiDevice androidx.test.uiautomator  Until androidx.test.uiautomator  descContains androidx.test.uiautomator.By  text androidx.test.uiautomator.By  focused $androidx.test.uiautomator.BySelector  By "androidx.test.uiautomator.UiDevice  CAROUSEL_DESCRIPTION "androidx.test.uiautomator.UiDevice  CATEGORIES_TAB_LABEL "androidx.test.uiautomator.UiDevice  FAVOURITES_TAB_LABEL "androidx.test.uiautomator.UiDevice  INITIAL_WAIT_TIMEOUT "androidx.test.uiautomator.UiDevice  MOVIES_TAB_LABEL "androidx.test.uiautomator.UiDevice  PROFILE_BUTTON_DESCRIPTION "androidx.test.uiautomator.UiDevice  SEARCH_TAB_DESCRIPTION "androidx.test.uiautomator.UiDevice  SHOWS_TAB_LABEL "androidx.test.uiautomator.UiDevice  TV_SHOWS_CHIP_LABEL "androidx.test.uiautomator.UiDevice  Until "androidx.test.uiautomator.UiDevice  WAIT_TIMEOUT "androidx.test.uiautomator.UiDevice  pressDPadCenter "androidx.test.uiautomator.UiDevice  
pressDPadDown "androidx.test.uiautomator.UiDevice  
pressDPadLeft "androidx.test.uiautomator.UiDevice  pressDPadRight "androidx.test.uiautomator.UiDevice  pressDPadUp "androidx.test.uiautomator.UiDevice  repeat "androidx.test.uiautomator.UiDevice  run "androidx.test.uiautomator.UiDevice  wait "androidx.test.uiautomator.UiDevice  waitForIdle "androidx.test.uiautomator.UiDevice  
findObject androidx.test.uiautomator.Until  
AndroidJUnit4 com.google.jetstream.benchmark  BaselineProfileGenerator com.google.jetstream.benchmark  BaselineProfileMode com.google.jetstream.benchmark  BaselineProfileRule com.google.jetstream.benchmark  By com.google.jetstream.benchmark  CAROUSEL_DESCRIPTION com.google.jetstream.benchmark  CATEGORIES_TAB_LABEL com.google.jetstream.benchmark  CompilationMode com.google.jetstream.benchmark  FAVOURITES_TAB_LABEL com.google.jetstream.benchmark  HOME_TAB_LABEL com.google.jetstream.benchmark  INITIAL_WAIT_TIMEOUT com.google.jetstream.benchmark  JETSTREAM_PACKAGE_NAME com.google.jetstream.benchmark  MOVIES_TAB_LABEL com.google.jetstream.benchmark  MacrobenchmarkRule com.google.jetstream.benchmark  PROFILE_BUTTON_DESCRIPTION com.google.jetstream.benchmark  Rule com.google.jetstream.benchmark  RunWith com.google.jetstream.benchmark  SEARCH_TAB_DESCRIPTION com.google.jetstream.benchmark  SHOWS_TAB_LABEL com.google.jetstream.benchmark  STARTUP_TEST_ITERATIONS com.google.jetstream.benchmark  StartupBenchmark com.google.jetstream.benchmark  StartupMode com.google.jetstream.benchmark  StartupTimingMetric com.google.jetstream.benchmark  TV_SHOWS_CHIP_LABEL com.google.jetstream.benchmark  Test com.google.jetstream.benchmark  Until com.google.jetstream.benchmark  WAIT_TIMEOUT com.google.jetstream.benchmark  listOf com.google.jetstream.benchmark  repeat com.google.jetstream.benchmark  run com.google.jetstream.benchmark  BaselineProfileRule 7com.google.jetstream.benchmark.BaselineProfileGenerator  By 7com.google.jetstream.benchmark.BaselineProfileGenerator  CAROUSEL_DESCRIPTION 7com.google.jetstream.benchmark.BaselineProfileGenerator  CATEGORIES_TAB_LABEL 7com.google.jetstream.benchmark.BaselineProfileGenerator  FAVOURITES_TAB_LABEL 7com.google.jetstream.benchmark.BaselineProfileGenerator  INITIAL_WAIT_TIMEOUT 7com.google.jetstream.benchmark.BaselineProfileGenerator  JETSTREAM_PACKAGE_NAME 7com.google.jetstream.benchmark.BaselineProfileGenerator  MOVIES_TAB_LABEL 7com.google.jetstream.benchmark.BaselineProfileGenerator  PROFILE_BUTTON_DESCRIPTION 7com.google.jetstream.benchmark.BaselineProfileGenerator  SEARCH_TAB_DESCRIPTION 7com.google.jetstream.benchmark.BaselineProfileGenerator  SHOWS_TAB_LABEL 7com.google.jetstream.benchmark.BaselineProfileGenerator  TV_SHOWS_CHIP_LABEL 7com.google.jetstream.benchmark.BaselineProfileGenerator  Until 7com.google.jetstream.benchmark.BaselineProfileGenerator  WAIT_TIMEOUT 7com.google.jetstream.benchmark.BaselineProfileGenerator  baselineProfileRule 7com.google.jetstream.benchmark.BaselineProfileGenerator  repeat 7com.google.jetstream.benchmark.BaselineProfileGenerator  run 7com.google.jetstream.benchmark.BaselineProfileGenerator  BaselineProfileMode /com.google.jetstream.benchmark.StartupBenchmark  CompilationMode /com.google.jetstream.benchmark.StartupBenchmark  JETSTREAM_PACKAGE_NAME /com.google.jetstream.benchmark.StartupBenchmark  MacrobenchmarkRule /com.google.jetstream.benchmark.StartupBenchmark  STARTUP_TEST_ITERATIONS /com.google.jetstream.benchmark.StartupBenchmark  StartupMode /com.google.jetstream.benchmark.StartupBenchmark  StartupTimingMetric /com.google.jetstream.benchmark.StartupBenchmark  
benchmarkRule /com.google.jetstream.benchmark.StartupBenchmark  listOf /com.google.jetstream.benchmark.StartupBenchmark  startup /com.google.jetstream.benchmark.StartupBenchmark  	Function1 kotlin  repeat kotlin  run kotlin  List kotlin.collections  listOf kotlin.collections  repeat kotlin.text  Rule 	org.junit  Test 	org.junit  RunWith org.junit.runner                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             