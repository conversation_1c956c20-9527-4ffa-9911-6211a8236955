package com.google.jetstream;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.jetstream.data.repositories.MovieCastDataSource;
import com.google.jetstream.data.repositories.MovieCategoryDataSource;
import com.google.jetstream.data.repositories.MovieDataSource;
import com.google.jetstream.data.repositories.MovieRepositoryImpl;
import com.google.jetstream.data.repositories.TvDataSource;
import com.google.jetstream.data.util.AssetsReader;
import com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel;
import com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel;
import com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel;
import com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.home.HomeScreeViewModel;
import com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules;
import com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.home.HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.movielist.MovieListScreenViewModel;
import com.google.jetstream.presentation.screens.movielist.MovieListScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.movielist.MovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.movielist.MovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel;
import com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel;
import com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.search.SearchScreenViewModel;
import com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.search.SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.shows.ShowScreenViewModel;
import com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.shows.ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel;
import com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules;
import com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey;
import com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.managers.SavedStateHandleHolder;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.LazyClassKeyMap;
import dagger.internal.Preconditions;
import dagger.internal.Provider;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DaggerJetStreamApplication_HiltComponents_SingletonC {
  private DaggerJetStreamApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    public JetStreamApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements JetStreamApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private SavedStateHandleHolder savedStateHandleHolder;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ActivityRetainedCBuilder savedStateHandleHolder(
        SavedStateHandleHolder savedStateHandleHolder) {
      this.savedStateHandleHolder = Preconditions.checkNotNull(savedStateHandleHolder);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ActivityRetainedC build() {
      Preconditions.checkBuilderRequirement(savedStateHandleHolder, SavedStateHandleHolder.class);
      return new ActivityRetainedCImpl(singletonCImpl, savedStateHandleHolder);
    }
  }

  private static final class ActivityCBuilder implements JetStreamApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements JetStreamApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements JetStreamApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements JetStreamApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements JetStreamApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements JetStreamApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public JetStreamApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends JetStreamApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends JetStreamApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }
  }

  private static final class ViewCImpl extends JetStreamApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends JetStreamApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity arg0) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Map<Class<?>, Boolean> getViewModelKeys() {
      return LazyClassKeyMap.<Boolean>of(ImmutableMap.<String, Boolean>builderWithExpectedSize(10).put(CategoriesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, CategoriesScreenViewModel_HiltModules.KeyModule.provide()).put(CategoryMovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, CategoryMovieListScreenViewModel_HiltModules.KeyModule.provide()).put(FavouriteScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, FavouriteScreenViewModel_HiltModules.KeyModule.provide()).put(HomeScreeViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, HomeScreeViewModel_HiltModules.KeyModule.provide()).put(MovieDetailsScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, MovieDetailsScreenViewModel_HiltModules.KeyModule.provide()).put(MovieListScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, MovieListScreenViewModel_HiltModules.KeyModule.provide()).put(MoviesScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, MoviesScreenViewModel_HiltModules.KeyModule.provide()).put(SearchScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, SearchScreenViewModel_HiltModules.KeyModule.provide()).put(ShowScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, ShowScreenViewModel_HiltModules.KeyModule.provide()).put(VideoPlayerScreenViewModel_HiltModules_KeyModule_Provide_LazyMapKey.lazyClassKeyName, VideoPlayerScreenViewModel_HiltModules.KeyModule.provide()).build());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }
  }

  private static final class ViewModelCImpl extends JetStreamApplication_HiltComponents.ViewModelC {
    private final SavedStateHandle savedStateHandle;

    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<CategoriesScreenViewModel> categoriesScreenViewModelProvider;

    private Provider<CategoryMovieListScreenViewModel> categoryMovieListScreenViewModelProvider;

    private Provider<FavouriteScreenViewModel> favouriteScreenViewModelProvider;

    private Provider<HomeScreeViewModel> homeScreeViewModelProvider;

    private Provider<MovieDetailsScreenViewModel> movieDetailsScreenViewModelProvider;

    private Provider<MovieListScreenViewModel> movieListScreenViewModelProvider;

    private Provider<MoviesScreenViewModel> moviesScreenViewModelProvider;

    private Provider<SearchScreenViewModel> searchScreenViewModelProvider;

    private Provider<ShowScreenViewModel> showScreenViewModelProvider;

    private Provider<VideoPlayerScreenViewModel> videoPlayerScreenViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.savedStateHandle = savedStateHandleParam;
      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.categoriesScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.categoryMovieListScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.favouriteScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.homeScreeViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.movieDetailsScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
      this.movieListScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 5);
      this.moviesScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 6);
      this.searchScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 7);
      this.showScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 8);
      this.videoPlayerScreenViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 9);
    }

    @Override
    public Map<Class<?>, javax.inject.Provider<ViewModel>> getHiltViewModelMap() {
      return LazyClassKeyMap.<javax.inject.Provider<ViewModel>>of(ImmutableMap.<String, javax.inject.Provider<ViewModel>>builderWithExpectedSize(10).put(CategoriesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) categoriesScreenViewModelProvider)).put(CategoryMovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) categoryMovieListScreenViewModelProvider)).put(FavouriteScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) favouriteScreenViewModelProvider)).put(HomeScreeViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) homeScreeViewModelProvider)).put(MovieDetailsScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) movieDetailsScreenViewModelProvider)).put(MovieListScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) movieListScreenViewModelProvider)).put(MoviesScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) moviesScreenViewModelProvider)).put(SearchScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) searchScreenViewModelProvider)).put(ShowScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) showScreenViewModelProvider)).put(VideoPlayerScreenViewModel_HiltModules_BindsModule_Binds_LazyMapKey.lazyClassKeyName, ((Provider) videoPlayerScreenViewModelProvider)).build());
    }

    @Override
    public Map<Class<?>, Object> getHiltViewModelAssistedMap() {
      return ImmutableMap.<Class<?>, Object>of();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.google.jetstream.presentation.screens.categories.CategoriesScreenViewModel 
          return (T) new CategoriesScreenViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 1: // com.google.jetstream.presentation.screens.categories.CategoryMovieListScreenViewModel 
          return (T) new CategoryMovieListScreenViewModel(viewModelCImpl.savedStateHandle, singletonCImpl.movieRepositoryImplProvider.get());

          case 2: // com.google.jetstream.presentation.screens.favourites.FavouriteScreenViewModel 
          return (T) new FavouriteScreenViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 3: // com.google.jetstream.presentation.screens.home.HomeScreeViewModel 
          return (T) new HomeScreeViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 4: // com.google.jetstream.presentation.screens.movies.MovieDetailsScreenViewModel 
          return (T) new MovieDetailsScreenViewModel(viewModelCImpl.savedStateHandle, singletonCImpl.movieRepositoryImplProvider.get());

          case 5: // com.google.jetstream.presentation.screens.movielist.MovieListScreenViewModel 
          return (T) new MovieListScreenViewModel(viewModelCImpl.savedStateHandle, singletonCImpl.movieRepositoryImplProvider.get());

          case 6: // com.google.jetstream.presentation.screens.movies.MoviesScreenViewModel 
          return (T) new MoviesScreenViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 7: // com.google.jetstream.presentation.screens.search.SearchScreenViewModel 
          return (T) new SearchScreenViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 8: // com.google.jetstream.presentation.screens.shows.ShowScreenViewModel 
          return (T) new ShowScreenViewModel(singletonCImpl.movieRepositoryImplProvider.get());

          case 9: // com.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenViewModel 
          return (T) new VideoPlayerScreenViewModel(viewModelCImpl.savedStateHandle, singletonCImpl.movieRepositoryImplProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends JetStreamApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl,
        SavedStateHandleHolder savedStateHandleHolderParam) {
      this.singletonCImpl = singletonCImpl;

      initialize(savedStateHandleHolderParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandleHolder savedStateHandleHolderParam) {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends JetStreamApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends JetStreamApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<MovieRepositoryImpl> movieRepositoryImplProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private AssetsReader assetsReader() {
      return new AssetsReader(ApplicationContextModule_ProvideContextFactory.provideContext(applicationContextModule));
    }

    private MovieDataSource movieDataSource() {
      return new MovieDataSource(assetsReader());
    }

    private TvDataSource tvDataSource() {
      return new TvDataSource(assetsReader());
    }

    private MovieCastDataSource movieCastDataSource() {
      return new MovieCastDataSource(assetsReader());
    }

    private MovieCategoryDataSource movieCategoryDataSource() {
      return new MovieCategoryDataSource(assetsReader());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.movieRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<MovieRepositoryImpl>(singletonCImpl, 0));
    }

    @Override
    public void injectJetStreamApplication(JetStreamApplication jetStreamApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.google.jetstream.data.repositories.MovieRepositoryImpl 
          return (T) new MovieRepositoryImpl(singletonCImpl.movieDataSource(), singletonCImpl.tvDataSource(), singletonCImpl.movieCastDataSource(), singletonCImpl.movieCategoryDataSource());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
