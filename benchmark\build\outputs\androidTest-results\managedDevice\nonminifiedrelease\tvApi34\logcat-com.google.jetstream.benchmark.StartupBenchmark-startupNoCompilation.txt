08-27 01:48:08.664  2480  2496 I TestRunner: started: startupNoCompilation(com.google.jetstream.benchmark.StartupBenchmark)
08-27 01:48:08.672  1107  1107 D CarrierSvcBindHelper: onHandleForceStop: [com.google.jetstream]
08-27 01:48:08.672  1107  1107 D CarrierSvcBindHelper: No carrier app for: 0
08-27 01:48:08.675  2480  2496 I UiAutomationPermGranter: Permission: android.permission.WRITE_EXTERNAL_STORAGE is already granted!
08-27 01:48:08.676  2480  2496 I UiAutomationPermGranter: Permission: android.permission.READ_EXTERNAL_STORAGE is already granted!
08-27 01:48:08.678  2480  2496 E TestRunner: assumption failed: startupNoCompilation(com.google.jetstream.benchmark.StartupBenchmark)
08-27 01:48:08.681  2480  2496 E TestRunner: ----- begin exception -----
08-27 01:48:08.681  2480  2496 E TestRunner: org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.Assume.assumeThat(Assume.java:106)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.Assume.assumeTrue(Assume.java:50)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:128)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.Suite.runChild(Suite.java:27)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
08-27 01:48:08.681  2480  2496 E TestRunner: 	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)
08-27 01:48:08.681  2480  2496 E TestRunner: ----- end exception -----
08-27 01:48:08.686  2480  2496 I TestRunner: finished: startupNoCompilation(com.google.jetstream.benchmark.StartupBenchmark)
