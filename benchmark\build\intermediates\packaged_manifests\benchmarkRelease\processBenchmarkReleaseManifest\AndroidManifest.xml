<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.google.jetstream.benchmark" >

    <uses-sdk
        android:minSdkVersion="28"
        android:targetSdkVersion="35" />

    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for com.google.jetstream.benchmark"
        android:targetPackage="com.google.jetstream.benchmark" />
    <!--
    This is needed to write benchmark report data to an external directory during instrumented
    tests to allow the accompanying androidx.benchmark gradle plugin to pull the reports onto host
     machine after the tests are done running.
    -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <!--
         Internet permission is required for perfetto trace shell processor http server but
        it's used to reach localhost only
    -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <queries>
        <package android:name="androidx.test.orchestrator" />
        <package android:name="androidx.test.services" />
        <package android:name="com.google.android.apps.common.testing.services" />
    </queries>

    <permission
        android:name="com.google.jetstream.benchmark.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.google.jetstream.benchmark.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:networkSecurityConfig="@xml/network_security_config" >
        <uses-library android:name="android.test.runner" />
        <!-- Activity used to block background content while benchmarks are running -->
        <activity
            android:name="androidx.benchmark.IsolationActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Light.NoTitleBar.Fullscreen" >
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundDialogTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name="androidx.tracing.perfetto.TracingReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >

            <!-- Note: DUMP above highly limits who can call the receiver; Shell has DUMP perm. -->
            <intent-filter>
                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING" />
                <action android:name="androidx.tracing.perfetto.action.ENABLE_TRACING_COLD_START" />
                <action android:name="androidx.tracing.perfetto.action.DISABLE_TRACING_COLD_START" />
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.google.jetstream.benchmark.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.tracing.perfetto.StartupTracingInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.tracing.perfetto.StartupTracingConfigStoreIsEnabledGate"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
        </receiver>
        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>