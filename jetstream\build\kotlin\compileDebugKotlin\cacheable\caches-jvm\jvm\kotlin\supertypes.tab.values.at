/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer7 6com.google.jetstream.data.repositories.MovieRepository kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelM Lcom.google.jetstream.presentation.screens.categories.CategoriesScreenUiStateM Lcom.google.jetstream.presentation.screens.categories.CategoriesScreenUiState androidx.lifecycle.ViewModelT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiStateT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiStateT Scom.google.jetstream.presentation.screens.categories.CategoryMovieListScreenUiState androidx.lifecycle.ViewModelL Kcom.google.jetstream.presentation.screens.favourites.FavouriteScreenUiStateL Kcom.google.jetstream.presentation.screens.favourites.FavouriteScreenUiState kotlin.Enum androidx.lifecycle.ViewModelA @com.google.jetstream.presentation.screens.home.HomeScreenUiStateA @com.google.jetstream.presentation.screens.home.HomeScreenUiStateA @com.google.jetstream.presentation.screens.home.HomeScreenUiState androidx.lifecycle.ViewModelK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiStateK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiStateK Jcom.google.jetstream.presentation.screens.movies.MovieDetailsScreenUiState androidx.lifecycle.ViewModelE Dcom.google.jetstream.presentation.screens.movies.MoviesScreenUiStateE Dcom.google.jetstream.presentation.screens.movies.MoviesScreenUiState kotlin.Enum androidx.lifecycle.ViewModel= <com.google.jetstream.presentation.screens.search.SearchState= <com.google.jetstream.presentation.screens.search.SearchState androidx.lifecycle.ViewModelB Acom.google.jetstream.presentation.screens.shows.ShowScreenUiStateB Acom.google.jetstream.presentation.screens.shows.ShowScreenUiState androidx.lifecycle.ViewModelO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiStateO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiStateO Ncom.google.jetstream.presentation.screens.videoPlayer.VideoPlayerScreenUiState kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiStateK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiStateK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiState kotlin.Enum androidx.lifecycle.ViewModelK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiStateK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiStateK Jcom.google.jetstream.presentation.screens.movielist.MovieListScreenUiState