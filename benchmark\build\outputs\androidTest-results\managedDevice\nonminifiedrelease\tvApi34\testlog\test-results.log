INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.BaselineProfileGenerator
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stream=
com.google.jetstream.benchmark.BaselineProfileGenerator:
INSTRUMENTATION_STATUS: test=startup
INSTRUMENTATION_STATUS_CODE: 1
INSTRUMENTATION_STATUS: additionalTestOutputFile_BaselineProfileGenerator_startup-baseline-prof.txt=/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt
INSTRUMENTATION_STATUS_CODE: 2
INSTRUMENTATION_STATUS: additionalTestOutputFile_BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt=/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt
INSTRUMENTATION_STATUS_CODE: 2
INSTRUMENTATION_STATUS: android.studio.display.benchmark=BaselineProfileGenerator_startup
Total run time Ns: 342707245900.
Baseline profile [results](file://BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt)

To copy the profile use:
adb -e pull "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt" .

INSTRUMENTATION_STATUS: android.studio.v2display.benchmark=BaselineProfileGenerator_startup
Total run time Ns: 342707245900.
Baseline profile [results](file://BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt)

To copy the profile use:
adb -e pull "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt" .

INSTRUMENTATION_STATUS: android.studio.v2display.benchmark.outputDirPath=/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output
INSTRUMENTATION_STATUS_CODE: 2
INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.BaselineProfileGenerator
INSTRUMENTATION_STATUS: current=1
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stream=.
INSTRUMENTATION_STATUS: test=startup
INSTRUMENTATION_STATUS_CODE: 0
INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.StartupBenchmark
INSTRUMENTATION_STATUS: current=2
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stream=
com.google.jetstream.benchmark.StartupBenchmark:
INSTRUMENTATION_STATUS: test=startupNoCompilation
INSTRUMENTATION_STATUS_CODE: 1
INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.StartupBenchmark
INSTRUMENTATION_STATUS: current=2
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stack=org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)

INSTRUMENTATION_STATUS: stream=
com.google.jetstream.benchmark.StartupBenchmark:
INSTRUMENTATION_STATUS: test=startupNoCompilation
INSTRUMENTATION_STATUS_CODE: -4
INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.StartupBenchmark
INSTRUMENTATION_STATUS: current=3
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stream=
INSTRUMENTATION_STATUS: test=startupBaselineProfile
INSTRUMENTATION_STATUS_CODE: 1
INSTRUMENTATION_STATUS: class=com.google.jetstream.benchmark.StartupBenchmark
INSTRUMENTATION_STATUS: current=3
INSTRUMENTATION_STATUS: id=AndroidJUnitRunner
INSTRUMENTATION_STATUS: numtests=3
INSTRUMENTATION_STATUS: stack=org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)

INSTRUMENTATION_STATUS: stream=
INSTRUMENTATION_STATUS: test=startupBaselineProfile
INSTRUMENTATION_STATUS_CODE: -4
INSTRUMENTATION_RESULT: stream=

Time: 343

OK (3 tests)


INSTRUMENTATION_CODE: -1
