com.google.jetstream.benchmark-tracing-1.1.0-0 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0480c8e45ee42471e6528401e0b309da\transformed\tracing-1.1.0\res
com.google.jetstream.benchmark-tracing-ktx-1.1.0-1 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\231aa3de063a18fd3611884b383eeb5f\transformed\tracing-ktx-1.1.0\res
com.google.jetstream.benchmark-benchmark-macro-1.3.3-2 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32b42cdb4e821d44e4581e72e1a09e4b\transformed\benchmark-macro-1.3.3\res
com.google.jetstream.benchmark-startup-runtime-1.1.1-3 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\34231b1949e223dcfaa6bc14cd865df8\transformed\startup-runtime-1.1.1\res
com.google.jetstream.benchmark-benchmark-macro-junit4-1.3.3-4 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\354d679e0a71a0b19dad5336a2dff54a\transformed\benchmark-macro-junit4-1.3.3\res
com.google.jetstream.benchmark-tracing-perfetto-binary-1.0.0-5 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3cfdef20986cb7e3b4d8b55b6c7d8c33\transformed\tracing-perfetto-binary-1.0.0\res
com.google.jetstream.benchmark-lifecycle-runtime-2.3.1-6 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\57bed926920ccbad333d4732be9297b9\transformed\lifecycle-runtime-2.3.1\res
com.google.jetstream.benchmark-uiautomator-2.3.0-7 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\82f1f116e1509d539f19562207d50213\transformed\uiautomator-2.3.0\res
com.google.jetstream.benchmark-runtime-release-8 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\889a5524482757677fcabb4602d38e43\transformed\runtime-release\res
com.google.jetstream.benchmark-core-1.6.1-9 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9000974cb21b928582af9a6ed44ed39c\transformed\core-1.6.1\res
com.google.jetstream.benchmark-annotation-experimental-1.4.1-10 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\959fa8efb021305fbf717a4560c175cc\transformed\annotation-experimental-1.4.1\res
com.google.jetstream.benchmark-tracing-perfetto-1.0.0-11 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a20ed9e31a30e86f0c6ae1bdb84454cb\transformed\tracing-perfetto-1.0.0\res
com.google.jetstream.benchmark-benchmark-common-1.3.3-12 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a7fd7ccd4fb4c1488d7875dcf6ed82d4\transformed\benchmark-common-1.3.3\res
com.google.jetstream.benchmark-profileinstaller-1.3.1-13 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cd95625f43e83eb8b0201e22141d2256\transformed\profileinstaller-1.3.1\res
com.google.jetstream.benchmark-core-1.9.0-14 C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f797fb1013f2abfa6ce826ef1c02abb9\transformed\core-1.9.0\res
com.google.jetstream.benchmark-pngs-15 E:\1-test\tv-samples\JetStreamCompose\benchmark\build\generated\res\pngs\nonMinifiedRelease
com.google.jetstream.benchmark-resValues-16 E:\1-test\tv-samples\JetStreamCompose\benchmark\build\generated\res\resValues\nonMinifiedRelease
com.google.jetstream.benchmark-mergeNonMinifiedReleaseResources-17 E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\incremental\nonMinifiedRelease\mergeNonMinifiedReleaseResources\merged.dir
com.google.jetstream.benchmark-mergeNonMinifiedReleaseResources-18 E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\incremental\nonMinifiedRelease\mergeNonMinifiedReleaseResources\stripped.dir
com.google.jetstream.benchmark-nonMinifiedRelease-19 E:\1-test\tv-samples\JetStreamCompose\benchmark\build\intermediates\merged_res\nonMinifiedRelease\mergeNonMinifiedReleaseResources
com.google.jetstream.benchmark-main-20 E:\1-test\tv-samples\JetStreamCompose\benchmark\src\main\res
com.google.jetstream.benchmark-nonMinifiedRelease-21 E:\1-test\tv-samples\JetStreamCompose\benchmark\src\nonMinifiedRelease\res
