信息: Constructing runner from config.
信息: Configuring Android Instrumentation driver: android_instrumentation_runtime {
  instrumentation_info {
    app_package: "com.google.jetstream"
    test_package: "com.google.jetstream.benchmark"
    test_runner_class: "androidx.test.runner.AndroidJUnitRunner"
  }
  instrumentation_args {
    args_map {
      key: "additionalTestOutputDir"
      value: "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output"
    }
    args_map {
      key: "androidx.benchmark.enabledRules"
      value: "baselineprofile"
    }
    args_map {
      key: "androidx.benchmark.suppressErrors"
      value: "EMULATOR"
    }
    args_map {
      key: "androidx.benchmark.targetPackageName"
      value: "com.google.jetstream"
    }
  }
}
am_instrument_timeout: 31536000

信息: Configuring AndroidTestApkInstallerPlugin: apks_to_install {
  apk_paths: "E:\\1-test\\tv-samples\\JetStreamCompose\\jetstream\\build\\outputs\\apk\\nonMinifiedRelease\\jetstream-nonMinifiedRelease.apk"
  install_options {
  }
  uninstall_after_test: true
  force_reinstall_before_test: true
}
apks_to_install {
  apk_paths: "E:\\1-test\\tv-samples\\JetStreamCompose\\benchmark\\build\\outputs\\apk\\nonMinifiedRelease\\benchmark-nonMinifiedRelease.apk"
  install_options {
  }
  uninstall_after_test: true
  force_reinstall_before_test: true
}

信息: No installables found in test fixture. Nothing to install.
信息: Launching Emulator (Attempt 1)
信息: INFO         | Android emulator version 36.1.9.0 (build_id 13823996) (CL:N/A)
信息: INFO         | Graphics backend: gfxstream
信息: INFO         | Found systemPath D:\Android\Sdk\system-images\android-34\default\x86_64\
信息: Finding a test device :benchmark:tvApi34NonMinifiedReleaseAndroidTest (attempt 1 of 20).
Found 1 devices:
Television_1080p(emulator-5554)
信息: I0827 09:41:27.859038    3860 opengles.cpp:291] android_startOpenglesRenderer: gpu info
信息: I0827 09:41:27.859061    3860 opengles.cpp:292] GPU #1
信息:   Make: 10de
信息:   Model: NVIDIA GeForce GTX 1060 3GB
信息:   Device ID: 1c02
信息: 
信息: INFO         | IPv4 server found: ********
信息: INFO         | Ignore IPv6 address: 8877:4f63:5f02:0:6068:4f63:5f02:0
信息: INFO         | Ignore IPv6 address: 8877:4f63:5f02:0:6068:4f63:5f02:0
信息: INFO         | Ignore IPv6 address: 8877:4f63:5f02:0:6068:4f63:5f02:0
信息: INFO         | Guest GLES Driver: Auto (ext controls)
信息: library_mode host gpu mode host
信息: INFO         | emuglConfig_get_vulkan_hardware_gpu_support_info: Found physical GPU 'NVIDIA GeForce GTX 1060 3GB', type: VK_PHYSICAL_DEVICE_TYPE_DISCRETE_GPU, apiVersion: 1.3.280, driverVersion: 560.94
信息: 
信息: INFO         | Enabled VulkanAllocateHostMemory feature for gpu vendor NVIDIA GeForce GTX 1060 3GB on Linux
信息: 
信息: INFO         | GPU device local memory = 3179MB
信息: INFO         | Checking system compatibility:
信息: INFO         |   Checking: hasCompatibleHypervisor
信息: INFO         |      Ok: Hypervisor compatibility to run avd: `dev34_default_x86_64_Television__1080p_` are met
信息: INFO         |   Checking: hasSufficientSystem
信息: INFO         |      Ok: System requirements to run avd: `dev34_default_x86_64_Television__1080p_` are met
信息: INFO         |   Checking: hasSufficientHwGpu
信息: INFO         |      Ok: Hardware GPU requirements to run avd: `dev34_default_x86_64_Television__1080p_` are passed
信息: INFO         |   Checking: hasSufficientDiskSpace
信息: INFO         |      Ok: Disk space requirements to run avd: `dev34_default_x86_64_Television__1080p_` are met
信息: INFO         | Storing crashdata in: C:\Users\<USER>\AppData\Local\Temp\\AndroidEmulator\emu-crash-36.1.9.db, detection is enabled for process: 18616
信息: INFO         | Initializing hardware OpenGLES emulation support
信息: INFO         | HealthMonitor disabled.
信息: INFO         | SharedLibrary::open for [vulkan-1.dll]
信息: INFO         | SharedLibrary::open for [vulkan-1.dll]: not found in map, open for the first time
信息: INFO         | SharedLibrary::open for [vulkan-1.dll] (win32): call LoadLibrary
信息: INFO         | SharedLibrary::open succeeded for [vulkan-1.dll]. File name: [C:\Windows\SYSTEM32\vulkan-1.dll]
信息: INFO         | Added library: vulkan-1.dll
信息: INFO         | Selecting Vulkan device: NVIDIA GeForce GTX 1060 3GB, Version: 1.3.280
信息: INFO         | Disabling sparse binding feature support
信息: INFO         | SharedLibrary::open for [opengl32.dll]: not found in map, open for the first time
信息: INFO         | SharedLibrary::open for [opengl32.dll] (win32): call LoadLibrary
信息: INFO         | SharedLibrary::open succeeded for [opengl32.dll]. File name: [C:\Windows\SYSTEM32\opengl32.dll]
信息: INFO         | SharedLibrary::open for [libshadertranslator.dll]: not found in map, open for the first time
信息: INFO         | SharedLibrary::open for [libshadertranslator.dll] (win32): call LoadLibrary
信息: INFO         | SharedLibrary::open succeeded for [libshadertranslator.dll]. File name: [D:\Android\Sdk\emulator\lib64\libshadertranslator.dll]
信息: INFO         | Initializing VkEmulation features:
信息: INFO         |     glInteropSupported: true
信息: INFO         |     useDeferredCommands: true
信息: INFO         |     createResourceWithRequirements: true
信息: INFO         |     useVulkanComposition: false
信息: INFO         |     useVulkanNativeSwapchain: false
信息: INFO         |     enable guestRenderDoc: false
信息: INFO         |     ASTC LDR emulation mode: Gpu
信息: INFO         |     enable ETC2 emulation: true
信息: INFO         |     enable Ycbcr emulation: false
信息: INFO         |     guestVulkanOnly: false
信息: INFO         |     useDedicatedAllocations: false
信息: INFO         | Graphics Adapter Vendor Google (NVIDIA Corporation)
信息: INFO         | Graphics Adapter Android Emulator OpenGL ES Translator (NVIDIA GeForce GTX 1060 3GB/PCIe/SSE2)
信息: INFO         | Graphics API Version OpenGL ES 3.0 (4.5.0 NVIDIA 560.94)
信息: I0827 09:41:29.286054    3860 userspace-boot-properties.cpp:766] Userspace boot properties:
信息: I0827 09:41:29.286079    3860 userspace-boot-properties.cpp:770]   androidboot.boot_devices=pci0000:00/0000:00:03.0 pci0000:00/0000:00:06.0
信息: I0827 09:41:29.286087    3860 userspace-boot-properties.cpp:770]   androidboot.dalvik.vm.heapsize=512m
信息: I0827 09:41:29.286092    3860 userspace-boot-properties.cpp:770]   androidboot.debug.hwui.renderer=skiagl
信息: I0827 09:41:29.286096    3860 userspace-boot-properties.cpp:770]   androidboot.debug.sf.nobootanimation=1
信息: I0827 09:41:29.286100    3860 userspace-boot-properties.cpp:770]   androidboot.hardware=ranchu
信息: I0827 09:41:29.286104    3860 userspace-boot-properties.cpp:770]   androidboot.hardware.gltransport=pipe
信息: I0827 09:41:29.286108    3860 userspace-boot-properties.cpp:770]   androidboot.hardware.vulkan=ranchu
信息: I0827 09:41:29.286112    3860 userspace-boot-properties.cpp:770]   androidboot.logcat=*:V
信息: I0827 09:41:29.286115    3860 userspace-boot-properties.cpp:770]   androidboot.opengles.version=196609
信息: I0827 09:41:29.286120    3860 userspace-boot-properties.cpp:770]   androidboot.qemu=1
信息: I0827 09:41:29.286124    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.adb.pubkey=QAAAAFeSxj2Zhm/Q3ngTZ8vMVaCgvbesrbnV1E5iIEsRe5YgCLfiCr0/2MMbNI3OxCjUXM/Qab/VkyTnrnYAoh1Xp/NruAYSj1ChV2rlJxgxUdB7mVPWjiqgjsjs4bwa7YbiZ3VLSHuXVhEscLxjJseocfhitfFrKC4UXqIbbOkhuF4sRSIUuqTZa+Ji+2sDKXga3beoEZB+JdlijGFxLzL7VfEJtnt6ykwJ3w2EJPhGtxYcd0BW4YYxTnpT2u3sTjyAlGJ/wy4/oT/aBKaWFA28STAM+sVPn1vKAyEq6pGqf1qOUDkBh6hY71gASSVAL45CF9C2zkz7YxJymYOlJMmfabs5OrvH29RIWb/8JB++a+i3BbXX8DNb4a5ReEWa6sVRU4djJRO4YBUAEm5To2HJvXOi4QayBd87Q8D0c/gGdDt9rxa3mvHKKEc654KLKKJ99mVAQw6cLvmVo8S97NR671wqSEVsX3pIZw/13VIdDvwzla0Oi6v1aJm/wrxG6LffHAqJoicJE9neTvzmzqO9j45Ytnvb2p9o8qydQ+6P8y+Zc45xJYMQQAnCMxbK7XWZoxNEbbbu/LkhFTUozegNvSqkqu899DyzbtHWJ4smwufJYTgshXCLTsEZXyLt+hxGmVFhg3Lzg2aqexJL7KN8A0PO82IOtpS+2QX00ArTpOTLvuGgnAEAAQA= @unknown
信息: I0827 09:41:29.286137    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.avd_name=dev34_default_x86_64_Television__1080p_
信息: I0827 09:41:29.286141    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.camera_hq_edge_processing=0
信息: I0827 09:41:29.286146    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.camera_protocol_ver=1
信息: I0827 09:41:29.286150    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.cpuvulkan.version=4202496
信息: I0827 09:41:29.286165    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.gltransport.drawFlushInterval=800
信息: I0827 09:41:29.286173    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.gltransport.name=pipe
信息: I0827 09:41:29.286177    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.hwcodec.avcdec=2
信息: I0827 09:41:29.286181    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.hwcodec.hevcdec=2
信息: I0827 09:41:29.286185    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.hwcodec.vpxdec=2
信息: I0827 09:41:29.286191    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.settings.system.screen_off_timeout=2147483647
信息: I0827 09:41:29.286194    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.virtiowifi=1
信息: I0827 09:41:29.286198    3860 userspace-boot-properties.cpp:770]   androidboot.qemu.vsync=60
信息: I0827 09:41:29.286202    3860 userspace-boot-properties.cpp:770]   androidboot.serialno=EMULATOR36X1X9X0
信息: I0827 09:41:29.286206    3860 userspace-boot-properties.cpp:770]   androidboot.vbmeta.digest=8bee4f6105b6acd7de887b1ee6629ab07066df02ee9bbca07663adffdad97fc1
信息: I0827 09:41:29.286210    3860 userspace-boot-properties.cpp:770]   androidboot.vbmeta.hash_alg=sha256
信息: I0827 09:41:29.286213    3860 userspace-boot-properties.cpp:770]   androidboot.vbmeta.size=6656
信息: I0827 09:41:29.286216    3860 userspace-boot-properties.cpp:770]   androidboot.veritymode=enforcing
信息: INFO         | Graphics API Extensions GL_OES_EGL_sync GL_OES_EGL_image GL_OES_EGL_image_external GL_OES_depth24 GL_OES_depth32 GL_OES_element_index_uint GL_OES_texture_float GL_OES_texture_float_linear GL_OES_compressed_paletted_texture GL_OES_compressed_ETC1_RGB8_texture GL_OES_depth_texture GL_OES_texture_half_float GL_OES_texture_half_float_linear GL_OES_packed_depth_stencil GL_OES_vertex_half_float GL_OES_texture_npot GL_OES_rgb8_rgba8 GL_OVR_multiview2 GL_EXT_multiview_texture_multisample GL_EXT_color_buffer_float GL_EXT_color_buffer_half_float GL_EXT_texture_format_BGRA8888 GL_APPLE_texture_format_BGRA8888 GL_EXT_texture_buffer 
信息: INFO         | Graphics Device Extensions N/A
信息: INFO         | Sending adb public key [QAAAAFeSxj2Zhm/Q3ngTZ8vMVaCgvbesrbnV1E5iIEsRe5YgCLfiCr0/2MMbNI3OxCjUXM/Qab/VkyTnrnYAoh1Xp/NruAYSj1ChV2rlJxgxUdB7mVPWjiqgjsjs4bwa7YbiZ3VLSHuXVhEscLxjJseocfhitfFrKC4UXqIbbOkhuF4sRSIUuqTZa+Ji+2sDKXga3beoEZB+JdlijGFxLzL7VfEJtnt6ykwJ3w2EJPhGtxYcd0BW4YYxTnpT2u3sTjyAlGJ/wy4/oT/aBKaWFA28STAM+sVPn1vKAyEq6pGqf1qOUDkBh6hY71gASSVAL45CF9C2zkz7YxJymYOlJMmfabs5OrvH29RIWb/8JB++a+i3BbXX8DNb4a5ReEWa6sVRU4djJRO4YBUAEm5To2HJvXOi4QayBd87Q8D0c/gGdDt9rxa3mvHKKEc654KLKKJ99mVAQw6cLvmVo8S97NR671wqSEVsX3pIZw/13VIdDvwzla0Oi6v1aJm/wrxG6LffHAqJoicJE9neTvzmzqO9j45Ytnvb2p9o8qydQ+6P8y+Zc45xJYMQQAnCMxbK7XWZoxNEbbbu/LkhFTUozegNvSqkqu899DyzbtHWJ4smwufJYTgshXCLTsEZXyLt+hxGmVFhg3Lzg2aqexJL7KN8A0PO82IOtpS+2QX00ArTpOTLvuGgnAEAAQA= @unknown]
信息: AEHD is operational
信息: INFO         | Activated packet streamer for bluetooth emulation
信息: host doesn't support requested feature: CPUID.01H:ECX.xsave [bit 26]
信息: host doesn't support requested feature: CPUID.01H:ECX.avx [bit 28]
信息: host doesn't support requested feature: CPUID.01H:ECX.xsave [bit 26]
信息: host doesn't support requested feature: CPUID.01H:ECX.avx [bit 28]
信息: INFO         | Monitoring duration of emulator setup.
信息: WARNING      | The emulator now requires a signed jwt token for gRPC access! Use the -grpc flag if you really want an open unprotected grpc port
信息: INFO         | Using security allow list from: D:\Android\Sdk\emulator\lib\emulator_access.json
信息: WARNING      | *** Basic token auth should only be used by android-studio ***
信息: INFO         | The active JSON Web Key Sets can be found here: C:\Users\<USER>\AppData\Local\Temp\avd\running\18616\jwks\e4d1657c-13ec-41c1-a06f-8b5c0d465f0d\active.jwk
信息: INFO         | Scanning C:\Users\<USER>\AppData\Local\Temp\avd\running\18616\jwks\e4d1657c-13ec-41c1-a06f-8b5c0d465f0d for jwk keys.
信息: INFO         | Started GRPC server at 127.0.0.1:8556, security: Local, auth: +token
信息: INFO         | Advertising in: C:\Users\<USER>\AppData\Local\Temp\avd\running\pid_18616.ini
信息: INFO         | Setting display: 0 configuration to: 1920x1080, dpi: 320x320 
信息: INFO         | setDisplayActiveConfig 0
信息: INFO         | Loading snapshot 'default_boot'...
信息: Finding a test device :benchmark:tvApi34NonMinifiedReleaseAndroidTest (attempt 2 of 20).
Found 1 devices:
Television_1080p(emulator-5554)
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Unknown XR viewport mode requested: 0, ignored.
信息: 
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: WARNING      | Client not connected yet. Ignoring message!
信息: INFO         | Successfully loaded snapshot 'default_boot' using 1019 ms
信息: Finding a test device :benchmark:tvApi34NonMinifiedReleaseAndroidTest (attempt 3 of 20).
Found 2 devices:
Television_1080p(emulator-5554)
:benchmark:tvApi34NonMinifiedReleaseAndroidTest(emulator-5556)
信息: sys.boot_completed=1 (emulator-5556)
信息: dev.bootcomplete=1 (emulator-5556)
信息: Package Manager is ready (emulator-5556)
警告: Line '[persist.sys.boot.reason.history]: [reboot,factory_reset,1756202905' doesnt match regexp '^\[([\w.\-]+)\]:\s\[(.*)\]$'. Discarding.
警告: Line 'reboot,1756202889]' doesnt match regexp '^\[([\w.\-]+)\]:\s\[(.*)\]$'. Discarding.
信息: Installing [E:\1-test\tv-samples\JetStreamCompose\jetstream\build\outputs\apk\nonMinifiedRelease\jetstream-nonMinifiedRelease.apk] on device emulator-5556.
信息: INFO         | IPv4 server found: ********
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | IPv4 server found: ********
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | Ignore IPv6 address: a8cf:1afb:5f02:0:80c0:1afb:5f02:0
信息: INFO         | IPv4 server found: ********
信息: INFO         | Ignore IPv6 address: 684d:56fb:5f02:0:403e:56fb:5f02:0
信息: INFO         | Ignore IPv6 address: 684d:56fb:5f02:0:403e:56fb:5f02:0
信息: INFO         | Ignore IPv6 address: 684d:56fb:5f02:0:403e:56fb:5f02:0
信息: Installing [E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\apk\nonMinifiedRelease\benchmark-nonMinifiedRelease.apk] on device emulator-5556.
信息: Start logcat streaming.
信息: Running Android Instrumentation driver.
信息: Copying files from device to host: /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34
信息: Copying /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34\BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt
警告: Adb call failed.
Exit code: 1
Command: D:\Android\Sdk\platform-tools\adb.exe -H localhost -P 5037 -s emulator-5556 shell [[ -d "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt" ]]
Timeout: PT2M
信息: Copying /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34\BaselineProfileGenerator_startup-baseline-prof.txt
警告: Adb call failed.
Exit code: 1
Command: D:\Android\Sdk\platform-tools\adb.exe -H localhost -P 5037 -s emulator-5556 shell [[ -d "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt" ]]
Timeout: PT2M
信息: Copying files from device to host: /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34
信息: Copying /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34\BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt
警告: Adb call failed.
Exit code: 1
Command: D:\Android\Sdk\platform-tools\adb.exe -H localhost -P 5037 -s emulator-5556 shell [[ -d "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof-2025-08-27-01-48-08.txt" ]]
Timeout: PT2M
信息: Copying /sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt to E:\1-test\tv-samples\JetStreamCompose\benchmark\build\outputs\managed_device_android_test_additional_output\nonminifiedrelease\tvApi34\BaselineProfileGenerator_startup-baseline-prof.txt
警告: Adb call failed.
Exit code: 1
Command: D:\Android\Sdk\platform-tools\adb.exe -H localhost -P 5037 -s emulator-5556 shell [[ -d "/sdcard/Android/media/com.google.jetstream.benchmark/additional_test_output/BaselineProfileGenerator_startup-baseline-prof.txt" ]]
Timeout: PT2M
信息: Stop logcat streaming.
信息: Uninstalling com.google.jetstream for device emulator-5556.
信息: Uninstalling com.google.jetstream.benchmark for device emulator-5556.
信息: Finding a test device :benchmark:tvApi34NonMinifiedReleaseAndroidTest (attempt 1 of 1).
Found 2 devices:
Television_1080p(emulator-5554)
:benchmark:tvApi34NonMinifiedReleaseAndroidTest(emulator-5556)
信息: INFO         | Wait for emulator (pid 18616) 20 seconds to shutdown gracefully before kill;you can set environment variable ANDROID_EMULATOR_WAIT_TIME_BEFORE_KILL(in seconds) to change the default value (20 seconds)
信息: 
信息: WARNING      | Not saving state: RAM not mapped as shared
信息: INFO         | Saving snapshot 'default_boot' using 6 ms
信息: Execute com.google.jetstream.benchmark.BaselineProfileGenerator.startup: PASSED
信息: Execute com.google.jetstream.benchmark.StartupBenchmark.startupNoCompilation: IGNORED
org.junit.AssumptionViolatedException: org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)

org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)
信息: Execute com.google.jetstream.benchmark.StartupBenchmark.startupBaselineProfile: IGNORED
org.junit.AssumptionViolatedException: org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)

org.junit.AssumptionViolatedException: got: <false>, expected: is <true>
	at org.junit.Assume.assumeThat(Assume.java:106)
	at org.junit.Assume.assumeTrue(Assume.java:50)
	at androidx.benchmark.macro.junit4.MacrobenchmarkRule$applyInternal$1.evaluate(MacrobenchmarkRule.kt:205)
	at androidx.test.rule.GrantPermissionRule$RequestPermissionStatement.evaluate(GrantPermissionRule.java:150)
	at org.junit.rules.RunRules.evaluate(RunRules.java:20)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.BlockJUnit4ClassRunner$1.evaluate(BlockJUnit4ClassRunner.java:100)
	at org.junit.runners.ParentRunner.runLeaf(ParentRunner.java:366)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:103)
	at org.junit.runners.BlockJUnit4ClassRunner.runChild(BlockJUnit4ClassRunner.java:63)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at androidx.test.ext.junit.runners.AndroidJUnit4.run(AndroidJUnit4.java:162)
	at org.junit.runners.Suite.runChild(Suite.java:128)
	at org.junit.runners.Suite.runChild(Suite.java:27)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:115)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:68)
	at androidx.test.internal.runner.TestExecutor.execute(TestExecutor.java:59)
	at androidx.test.runner.AndroidJUnitRunner.onStart(AndroidJUnitRunner.java:463)
	at android.app.Instrumentation$InstrumentationThread.run(Instrumentation.java:2402)
